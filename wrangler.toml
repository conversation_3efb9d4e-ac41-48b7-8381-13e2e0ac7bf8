compatibility_flags = ["nodejs_compat"]
compatibility_date = "2025-03-21"
name = "harmony-chat"
main = "src/index.js"

vars = { DEV_MODE = "true", TIMEZONE = "Asia/Bangkok", PROTOCOL = "http", HOSTNAME = "localhost:8787", TELEGRAM_BOT_USERNAME = "hrmnyBot", TELEGRAM_BOT_NAME = "Harmony", LANGCHAIN_TRACING_V2 = "true", LANGCHAIN_PROJECT = "harmony-chat-dev" }

[env.dev]
vars = {}

[env.production]
vars = { DEV_MODE = "false", TIMEZONE = "Asia/Bangkok", PROTOCOL = "https", HOSTNAME = "harmony-chat-production.nandemo.workers.dev", TELEGRAM_BOT_USERNAME = "hrmnyBot", TELEGRAM_BOT_NAME = "Harmony", LANGCHAIN_TRACING_V2 = "true", LANGCHAIN_PROJECT = "harmony-chat-production" }

[env.production.observability]
enabled = true

[placement]
mode = "off"
