export class MetricsTracker {
	constructor(userId) {
		this.startTime = Date.now();
		this.operationId = `${userId}-${this.startTime}`;
		this.metrics = {
			operationId: this.operationId,
			userId,
			startTime: this.startTime,
			aiProcessingTime: 0,
			redisOperationTime: 0,
			totalTime: 0,
			success: false,
			errorType: null,
			factsExtracted: 0,
			factsStored: 0,
		};
	}

	recordExtraction(count) {
		this.metrics.factsExtracted = count;
	}

	recordStorage(count) {
		this.metrics.factsStored = count;
	}

	getMetrics() {
		this.metrics.totalTime = Date.now() - this.startTime;
		return { ...this.metrics };
	}

	createSuccessResult(factsStored) {
		this.metrics.success = true;
		this.metrics.totalTime = Date.now() - this.startTime;

		if (this.metrics.factsExtracted > 0) {
			console.log(`[extractAndStoreFacts] Success for user ${this.metrics.userId}:`, {
				extracted: this.metrics.factsExtracted,
				stored: factsStored,
				totalTime: `${this.metrics.totalTime}ms`,
			});
		}

		return {
			success: true,
			factsStored,
			metrics: this.getMetrics(),
		};
	}
}