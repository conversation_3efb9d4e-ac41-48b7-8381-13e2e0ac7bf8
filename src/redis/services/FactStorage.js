import { getRedisClient } from '../redisClient.js';
import { FACT_REFINEMENT_PROMPT } from '../../constants.js';
import { callFastGenerativeAI } from '../../chat/geminiAI.js';

export class FactStorage {
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
	}

	async storeFacts(userId, facts) {
		if (!facts.length) return 0;

		const factsKey = `user:${userId}:facts`;
		const existingFacts = await this.redis.smembers(factsKey);
		const refinedFacts = await this._refineFacts(existingFacts, facts);

		await this._atomicStore(factsKey, refinedFacts);
		console.log(`[FactStorage] Stored ${refinedFacts.length} facts for user ${userId}`);
		return refinedFacts.length;
	}

	async getUserFacts(userId) {
		const factsKey = `user:${userId}:facts`;
		try {
			return await this.redis.smembers(factsKey);
		} catch (error) {
			console.error(`[FactStorage] Error getting facts for user ${userId}:`, error);
			return [];
		}
	}

	async _refineFacts(existingFacts, newFacts) {
		const allFacts = [...new Set([...existingFacts, ...newFacts])];

		if (existingFacts.length < 1 || newFacts.length < 1) return allFacts;

		const userPrompt = `<INSTRUCTIONS>Merge and deduplicate these facts</INSTRUCTIONS>\nExisting facts:\n${existingFacts.join(
			'\n'
		)}\n\nNew facts:\n${newFacts.join('\n')}`;

		const config = {
			temperature: 0.1,
			systemInstruction: FACT_REFINEMENT_PROMPT,
			inferenceProvider: 'cerebras',
		};

		const contents = [{ role: 'user', parts: [{ text: userPrompt }] }];
		const response = await callFastGenerativeAI(this.env, config, contents);

		if (!response) return allFacts;

		return response.text
			.split('\n')
			.map((fact) => fact.replace(/^[-•*]\s*/, ''))
			.filter((fact) => fact.length > 5);
	}

	async _atomicStore(factsKey, facts) {
		const pipeline = this.redis.pipeline();
		pipeline.del(factsKey);
		if (facts.length > 0) {
			pipeline.sadd(factsKey, ...facts);
		}
		await pipeline.exec();
	}
}
