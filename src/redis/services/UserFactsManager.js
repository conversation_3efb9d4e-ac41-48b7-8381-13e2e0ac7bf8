import { getRedisClient } from '../redisClient.js';

export class UserFactsManager {
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
	}

	async deleteUserFacts(userId) {
		const factsKey = `user:${userId}:facts`;
		
		try {
			const [mainDeleted, backupsDeleted] = await Promise.all([
				this._deleteMainFacts(factsKey, userId),
				this._deleteBackupFacts(userId)
			]);

			const success = mainDeleted && backupsDeleted;
			console.log(`[UserFactsManager] ${success ? 'Successfully deleted' : 'Partially deleted'} user facts for user ${userId}`);
			return success;
		} catch (error) {
			console.error(`[UserFactsManager] Error deleting user facts for user ${userId}:`, error);
			return false;
		}
	}

	async _deleteMainFacts(factsKey, userId) {
		const exists = await this.redis.exists(factsKey);
		if (!exists) {
			console.log(`[UserFactsManager] No user facts found for user ${userId}`);
			return true;
		}

		const result = await this.redis.del(factsKey);
		return result > 0;
	}

	async _deleteBackupFacts(userId) {
		const pattern = `user:${userId}:facts:backup:*`;
		
		try {
			const backupKeys = await this._scanKeys(pattern);
			if (!backupKeys.length) {
				console.log(`[UserFactsManager] No backup keys found for user ${userId}`);
				return true;
			}

			const results = await Promise.all(backupKeys.map(key => this.redis.del(key)));
			const deleted = results.reduce((sum, res) => sum + res, 0);
			console.log(`[UserFactsManager] Deleted ${deleted} backup keys for user ${userId}`);
			return deleted === backupKeys.length;
		} catch (error) {
			console.error(`[UserFactsManager] Error deleting backup facts for user ${userId}:`, error);
			return false;
		}
	}

	async _scanKeys(pattern) {
		let cursor = '0';
		let keys = [];
		
		do {
			const [newCursor, foundKeys] = await this.redis.scan(cursor, { match: pattern, count: 100 });
			cursor = newCursor;
			keys.push(...foundKeys);
		} while (cursor !== '0');
		
		return keys;
	}
}