import { MessageLogger } from './services/MessageLogger.js';
import { MessageRetriever } from './services/MessageRetriever.js';

export const logTelegramMessage = async (env, chatId, messageData) => {
	const logger = new MessageLogger(env);
	return logger.logMessage(chatId, messageData);
};

export const getPreviousMessages = async (env, chatId, botUsername) => {
	const retriever = new MessageRetriever(env);
	return retriever.getMessages(chatId, botUsername);
};