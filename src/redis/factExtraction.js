import { FactExtractor } from './services/FactExtractor.js';
import { FactStorage } from './services/FactStorage.js';
import { MetricsTracker } from './services/MetricsTracker.js';
import { ErrorHandler } from './services/ErrorHandler.js';

export const extractAndStoreFacts = async (env, userId, firstName, text, messageHistory) => {
	const metricsTracker = new MetricsTracker(userId);
	const errorHandler = new ErrorHandler();

	try {
		// Validate input
		if (!text) {
			return errorHandler.createEmptyInputError(metricsTracker.getMetrics());
		}

		// Extract facts
		const factExtractor = new FactExtractor(env);
		const extractedFacts = await factExtractor.extractFacts(firstName, text, messageHistory);
		metricsTracker.recordExtraction(extractedFacts.length);

		// Store facts
		const factStorage = new FactStorage(env);
		const storedCount = await factStorage.storeFacts(userId, extractedFacts);
		metricsTracker.recordStorage(storedCount);

		return metricsTracker.createSuccessResult(storedCount);
	} catch (error) {
		return errorHandler.handleError(error, metricsTracker.getMetrics());
	}
};

export const getUserFacts = async (env, userId) => {
	const factStorage = new FactStorage(env);
	return factStorage.getUserFacts(userId);
};
