import { ChatHistoryManager } from './services/ChatHistoryManager.js';
import { UserFactsManager } from './services/UserFactsManager.js';

export const deleteChatHistory = async (env, chatId, botUsername) => {
	const manager = new ChatHistoryManager(env);
	return manager.deleteChatHistory(chatId, botUsername);
};

export const deleteUserFacts = async (env, userId) => {
	const manager = new UserFactsManager(env);
	return manager.deleteUserFacts(userId);
};