/**
 * Hono middleware to verify the request's Host header against an expected hostname.
 * Allows bypass in development mode if DEV_MODE environment variable is 'true'.
 */
export const hostnameCheckMiddleware = async (c, next) => {
	const requestHost = c.req.header('Host');
	const expectedHost = c.env.HOSTNAME;

	// Log the hosts for debugging
	console.log(`Request Host: ${requestHost}, Expected Host: ${expectedHost}`);

	// Check if the hosts match
	if (requestHost !== expectedHost) {
		// If hosts don't match, check if DEV_MODE allows bypass
		if (c.env.DEV_MODE === 'true') {
			console.log('DEV_MODE enabled, bypassing hostname check.');
			return next(); // Bypass check in development mode
		}

		// If not in dev mode and hosts don't match, return 403 Forbidden
		console.warn(`Hostname mismatch: Request host "${requestHost}" does not match expected "${expectedHost}".`);
		return c.json(
			{
				status: 403,
				error: true,
				details: 'Forbidden: Hostname mismatch.',
			},
			403
		);
	}

	// If hosts match (or bypassed in dev mode), proceed
	await next();
};
