import { generateEmbedding } from '../chat/geminiAI.js';
import { getVectorStoreIndex } from '../vectorStore.js';

/**
 * Stores a message embedding in the vector database
 * @param {object} env - Environment variables
 * @param {object} webhookData - The original webhook data from Telegram
 * @param {string} text - The message text to embed
 * @param {number} chatId - The chat ID
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
export async function storeMessageEmbedding(env, webhookData, text, chatId) {
	try {
		// Only embed if there's text content
		if (!text || text.length === 0) {
			console.log(`[storeMessageEmbedding] No text content to embed for chat ${chatId}`);
			return false;
		}

		// Extract message details from webhook data
		const message = webhookData.message || (webhookData.callback_query && webhookData.callback_query.message);
		if (!message) {
			console.warn(`[storeMessageEmbedding] No message found in webhook data for chat ${chatId}`);
			return false;
		}

		const messageId = message.message_id;
		const messageDate = message.date;
		const userId = message.from?.id;
		const username = message.from?.username || 'unknown';
		const firstName = message.from?.first_name || '';

		// Generate embedding for the text
		const userMessageEmbedding = await generateEmbedding(env, text);
		if (!userMessageEmbedding) {
			console.warn(`[storeMessageEmbedding] Could not generate embedding for message from ${username} (${chatId})`);
			return false;
		}

		// Get vector store index
		const vectorIndex = getVectorStoreIndex(env);

		// Prepare data object for vector storage
		const dataObject = {
			id: messageId.toString(), // Ensure ID is a string
			vector: userMessageEmbedding,
			metadata: {
				text: text,
				chatId: chatId,
				userId: userId,
				username: username,
				firstName: firstName,
				timestamp: messageDate, // Unix timestamp from Telegram
				role: 'user',
			},
		};

		// Store in vector database
		await vectorIndex.upsert(dataObject);
		console.log(`[storeMessageEmbedding] Message from ${username} (${chatId}) embedded and stored in vector store`);
		return true;
	} catch (embeddingError) {
		console.error('[storeMessageEmbedding] Error during message embedding or vector store upsert:', embeddingError);
		// Don't throw the error - just log it and return false to allow processing to continue
		return false;
	}
}
