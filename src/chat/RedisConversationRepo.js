import { getRedisClient } from '../redis.js';

const CONVERSATION_STATE_KEY_PREFIX = 'conversation_state:';
const CONVERSATION_HISTORY_KEY_PREFIX = 'conversation_history:';

export class RedisConversationRepo {
	constructor(env) {
		this.redis = getRedisClient(env);
	}

	async getCurrentState(chatId) {
		try {
			const key = `${CONVERSATION_STATE_KEY_PREFIX}${chatId}`;
			const state = await this.redis.get(key);
			return state ? state : null;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error getting state for chat ${chatId}:`, error);
			return null;
		}
	}

	async updateConversationState(chatId, state) {
		try {
			const key = `${CONVERSATION_STATE_KEY_PREFIX}${chatId}`;
			await this.redis.set(key, JSON.stringify(state), { ex: 86400 }); // 24 hour TTL
			return true;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error updating state for chat ${chatId}:`, error);
			return false;
		}
	}

	async getLastActivity(chatId) {
		try {
			const key = `${CONVERSATION_HISTORY_KEY_PREFIX}${chatId}:last_activity`;
			const activity = await this.redis.get(key);
			return activity ? activity : null;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error getting last activity for chat ${chatId}:`, error);
			return null;
		}
	}

	async updateLastActivity(chatId, messageType = 'user') {
		try {
			const key = `${CONVERSATION_HISTORY_KEY_PREFIX}${chatId}:last_activity`;
			const activity = {
				timestamp: Date.now() / 1000,
				messageType,
			};
			await this.redis.set(key, JSON.stringify(activity), { ex: 604800 }); // 7 day TTL
			return true;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error updating last activity for chat ${chatId}:`, error);
			return false;
		}
	}

	async clearConversationData(chatId) {
		try {
			const stateKey = `${CONVERSATION_STATE_KEY_PREFIX}${chatId}`;
			const activityKey = `${CONVERSATION_HISTORY_KEY_PREFIX}${chatId}:last_activity`;

			await Promise.all([this.redis.del(stateKey), this.redis.del(activityKey)]);

			return true;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error clearing data for chat ${chatId}:`, error);
			return false;
		}
	}
}
