import { Client } from 'langsmith';

/**
 * Lang<PERSON>mith configuration and utilities for tracing LangChain operations
 */

let langsmithClient = null;

/**
 * Initialize LangSmith client with environment configuration
 * @param {object} env - Environment variables
 * @returns {Client|null} Lang<PERSON>mith client instance or null if not configured
 */
export function initialize<PERSON>ang<PERSON>mith(env) {
	// Check if Lang<PERSON><PERSON> tracing is enabled
	if (env.LANGCHAIN_TRACING_V2 !== 'true') {
		console.log('LangSmith tracing is disabled');
		return null;
	}

	// Check for required LangSmith configuration
	if (!env.LANGCHAIN_API_KEY) {
		console.warn('LangSmith API key not found. Tracing will be disabled.');
		return null;
	}

	try {
		// Initialize LangSmith client
		langsmithClient = new Client({
			apiKey: env.LANGCHAIN_API_KEY,
			apiUrl: env.LANGCHAIN_ENDPOINT || 'https://api.smith.langchain.com',
		});

		// Set global environment variables for LangChain tracing
		if (typeof globalThis !== 'undefined') {
			globalThis.process = globalThis.process || {};
			globalThis.process.env = globalThis.process.env || {};
			globalThis.process.env.LANGCHAIN_TRACING_V2 = env.LANGCHAIN_TRACING_V2;
			globalThis.process.env.LANGCHAIN_API_KEY = env.LANGCHAIN_API_KEY;
			globalThis.process.env.LANGCHAIN_PROJECT = env.LANGCHAIN_PROJECT || 'default';
			if (env.LANGCHAIN_ENDPOINT) {
				globalThis.process.env.LANGCHAIN_ENDPOINT = env.LANGCHAIN_ENDPOINT;
			}
		}

		console.log(`LangSmith tracing initialized for project: ${env.LANGCHAIN_PROJECT || 'default'}`);
		return langsmithClient;
	} catch (error) {
		console.error('Failed to initialize LangSmith:', error);
		return null;
	}
}

/**
 * Get the current LangSmith client instance
 * @returns {Client|null} LangSmith client or null if not initialized
 */
export function getLangSmithClient() {
	return langsmithClient;
}

/**
 * Create trace metadata for LangChain operations
 * @param {object} options - Trace metadata options
 * @param {string} options.operation - Operation type (e.g., 'chat_completion', 'embedding')
 * @param {string} options.model - Model name being used
 * @param {number} options.apiKeyIndex - API key index being used
 * @param {object} options.config - AI configuration object
 * @param {string} options.userId - User ID if available
 * @param {object} options.additional - Additional metadata
 * @returns {object} Trace metadata object
 */
export function createTraceMetadata({ operation, model, apiKeyIndex, config = {}, userId = null, additional = {} }) {
	return {
		operation,
		model,
		api_key_index: apiKeyIndex,
		temperature: config.temperature,
		response_mime_type: config.responseMimeType,
		tools_count: config.tools ? config.tools.length : 0,
		user_id: userId,
		timestamp: new Date().toISOString(),
		...additional,
	};
}

/**
 * Create a traced version of a function with LangSmith
 * @param {Function} fn - Function to trace
 * @param {string} name - Trace name
 * @param {object} metadata - Trace metadata
 * @returns {Function} Traced function
 */
export function withTracing(fn, name, metadata = {}) {
	return async (...args) => {
		if (!langsmithClient) {
			// If LangSmith is not initialized, just run the function normally
			return await fn(...args);
		}

		let runId;
		try {
			// Create a run for this operation
			runId = crypto.randomUUID();

			// Start the trace
			await langsmithClient.createRun({
				id: runId,
				name,
				run_type: 'llm',
				inputs: {
					args: args.length > 0 ? JSON.stringify(args[0]) : '{}',
				},
				extra: metadata,
				start_time: new Date(),
			});

			// Execute the function
			const startTime = Date.now();
			const result = await fn(...args);
			const endTime = Date.now();

			// End the trace with success
			await langsmithClient.updateRun(runId, {
				outputs: {
					result: typeof result === 'string' ? result : JSON.stringify(result),
				},
				end_time: new Date(),
				extra: {
					...metadata,
					duration_ms: endTime - startTime,
					success: true,
				},
			});

			return result;
		} catch (error) {
			// End the trace with error
			if (langsmithClient) {
				try {
					await langsmithClient.updateRun(runId, {
						error: error.message,
						end_time: new Date(),
						extra: {
							...metadata,
							success: false,
							error_type: error.constructor.name,
						},
					});
				} catch (traceError) {
					console.error('Failed to update trace with error:', traceError);
				}
			}
			throw error;
		}
	};
}

/**
 * Log a custom event to LangSmith
 * @param {string} name - Event name
 * @param {object} data - Event data
 * @param {object} metadata - Additional metadata
 */
export async function logEvent(name, data = {}, metadata = {}) {
	if (!langsmithClient) {
		return;
	}

	try {
		await langsmithClient.createRun({
			id: crypto.randomUUID(),
			name,
			run_type: 'tool',
			inputs: data,
			outputs: {},
			extra: {
				...metadata,
				event_type: 'custom',
				timestamp: new Date().toISOString(),
			},
			start_time: new Date(),
			end_time: new Date(),
		});
	} catch (error) {
		console.error('Failed to log event to LangSmith:', error);
	}
}
