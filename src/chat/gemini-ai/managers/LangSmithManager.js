/**
 * @fileoverview LangSmith initialization manager.
 */

import { initializeLangSmith } from '../../langsmithConfig.js';

/**
 * Manages LangSmith initialization
 */
export class LangSmithManager {
	constructor() {
		this.initialized = false;
	}

	/**
	 * Ensures <PERSON>Smith is initialized for the given environment
	 * @param {object} env - Environment variables
	 */
	ensureInitialized(env) {
		if (!this.initialized) {
			initializeLangSmith(env);
			this.initialized = true;
		}
	}
}
