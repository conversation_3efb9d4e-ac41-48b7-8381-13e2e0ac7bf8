/**
 * @fileoverview Embedding generation manager with caching and error handling.
 */

import { ModelFactory } from '../factories/ModelFactory.js';
import { GeminiAIError, ApiKeyError } from '../errors/GeminiErrors.js';

/**
 * Manages embedding generation with caching and error handling
 */
export class EmbeddingManager {
	constructor(apiKeyManager) {
		this.apiKeyManager = apiKeyManager;
		this.embeddingsInstance = null;
		this.currentApiKey = null;
	}

	/**
	 * Creates or reuses an embeddings instance
	 * @param {string} apiKey - Valid Gemini API key
	 * @returns {GoogleGenerativeAIEmbeddings} Embeddings instance
	 */
	getEmbeddingsInstance(apiKey) {
		if (!this.embeddingsInstance || this.currentApiKey !== apiKey) {
			this.embeddingsInstance = ModelFactory.createEmbeddingsInstance(apiKey);
			this.currentApiKey = apiKey;
		}
		return this.embeddingsInstance;
	}

	/**
	 * Generates an embedding for the given text with enhanced error handling
	 * @param {object} env - Environment variables
	 * @param {string} textToEmbed - Text to generate embedding for
	 * @returns {Promise<number[]>} Embedding vector
	 * @throws {GeminiAIError} If API key is missing or text is invalid
	 */
	async generateEmbedding(env, textToEmbed) {
		try {
			// Input validation
			if (!textToEmbed || typeof textToEmbed !== 'string' || textToEmbed.length === 0) {
				throw new GeminiAIError('Invalid text input for embedding: must be a non-empty string');
			}

			if (!env) {
				throw new GeminiAIError('Environment variables are required');
			}

			const apiKey = this.apiKeyManager.getNextGeminiApiKey(env);
			if (!apiKey) {
				throw new ApiKeyError('No Generative AI API key available for embedding');
			}

			// Create or reuse embeddings instance
			const embeddings = this.getEmbeddingsInstance(apiKey);

			// Generate embedding with enhanced context
			const result = await embeddings.embedQuery(textToEmbed);

			// Add basic validation for result
			if (!result || !Array.isArray(result) || result.length === 0) {
				throw new GeminiAIError('Empty or invalid embedding response from API');
			}

			return result;
		} catch (error) {
			// Enhanced error logging with context
			const errorContext = {
				textLength: textToEmbed?.length || 0,
				error: error.message,
				timestamp: new Date().toISOString(),
			};

			console.error('Embedding generation failed:', errorContext);

			if (error instanceof GeminiAIError) {
				throw error;
			}

			// Rethrow with specific error
			throw new GeminiAIError(`Failed to generate embedding: ${error.message}`, error);
		}
	}
}
