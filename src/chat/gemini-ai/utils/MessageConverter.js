/**
 * @fileoverview Message conversion utilities for LangChain compatibility.
 */

import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { GeminiAIError } from '../errors/GeminiErrors.js';

/**
 * Handles message conversion between different formats
 */
export class MessageConverter {
	/**
	 * Converts the current message format to LangChain message format
	 * @param {Array} contents - Array of message objects with role and parts
	 * @param {string} systemInstruction - System instruction to add
	 * @returns {Array} Array of LangChain-compatible messages
	 * @throws {GeminiAIError} If contents is invalid
	 */
	static convertToLangChainMessages(contents, systemInstruction = '') {
		if (!Array.isArray(contents)) {
			throw new GeminiAIError('Contents must be an array');
		}

		const messages = [];

		// Add system message if provided
		if (systemInstruction) {
			messages.push(new SystemMessage({ content: systemInstruction }));
		}

		// Convert each content item to LangChain format
		for (const content of contents) {
			if (!content || !content.parts || !Array.isArray(content.parts)) {
				console.warn('Skipping invalid content item:', content);
				continue;
			}

			// Handle multimodal content (text + attachments)
			const contentParts = [];

			// Add text parts
			const textParts = content.parts
				.filter((part) => part && part.text)
				.map((part) => part.text)
				.join('\n');

			if (textParts) {
				contentParts.push({
					type: 'text',
					text: textParts,
				});
			}

			// Add attachment parts (convert from inlineData format to LangChain image_url format)
			const attachmentParts = content.parts
				.filter((part) => part && part.inlineData)
				.map((part) => ({
					type: 'image_url',
					image_url: {
						url: `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`,
					},
				}));

			contentParts.push(...attachmentParts);

			// Only add message if there's content
			if (contentParts.length > 0) {
				if (contentParts.length === 1 && contentParts[0].type === 'text') {
					// Simple text message
					messages.push(new HumanMessage({ content: contentParts[0].text }));
				} else {
					// Multimodal message - use HumanMessage for proper multimodal support
					messages.push(
						new HumanMessage({
							content: contentParts,
						})
					);
				}
			}
		}

		return messages;
	}
}
