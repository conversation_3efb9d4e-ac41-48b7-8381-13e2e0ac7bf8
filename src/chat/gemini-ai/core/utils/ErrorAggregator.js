/**
 * Error aggregation utility class
 */
export class ErrorAggregator {
	constructor() {
		this.errors = new Map();
		this.lastError = null;
	}

	/**
	 * Adds an error for a specific context
	 * @param {string} context - Error context (e.g., 'gemini/model-name', 'groq/model-name')
	 * @param {Error} error - The error that occurred
	 */
	addError(context, error) {
		this.errors.set(context, error.message);
		this.lastError = error;
	}

	/**
	 * Creates a comprehensive error message
	 * @param {string} operation - The operation that failed
	 * @returns {string} Formatted error message
	 */
	createErrorMessage(operation) {
		const errorDetails = Array.from(this.errors.entries())
			.map(([context, message]) => `${context}: ${message}`)
			.join('; ');
		return `${operation} failed. Details: ${errorDetails}`;
	}

	/**
	 * Gets the last error that occurred
	 * @returns {Error|null} The last error
	 */
	getLastError() {
		return this.lastError;
	}

	/**
	 * Checks if any errors have been recorded
	 * @returns {boolean} True if errors exist
	 */
	hasErrors() {
		return this.errors.size > 0;
	}
}
