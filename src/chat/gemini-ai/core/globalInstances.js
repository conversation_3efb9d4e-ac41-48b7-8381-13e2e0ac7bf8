import { ApiKeyManager } from '../managers/ApiKeyManager.js';
import { LangSmithManager } from '../managers/LangSmithManager.js';
import { EmbeddingManager } from '../managers/EmbeddingManager.js';

// Global instances for backward compatibility
export const apiKeyManager = new ApiKeyManager();
export const langSmithManager = new LangSmithManager();
export const embeddingManager = new EmbeddingManager(apiKeyManager);
