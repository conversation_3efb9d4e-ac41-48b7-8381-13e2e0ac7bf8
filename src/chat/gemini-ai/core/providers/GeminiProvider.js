import { ModelFactory } from '../../factories/ModelFactory.js';
import { MessageConverter } from '../../utils/MessageConverter.js';
import { ApiKeyError } from '../../errors/GeminiErrors.js';
import { DEFAULT_GEMINI_MODELS } from '../../config/constants.js';

/**
 * Gemini provider management class
 */
export class GeminiProvider {
	constructor(apiKeyManager, langSmithManager) {
		this.apiKeyManager = apiKeyManager;
		this.langSmithManager = langSmithManager;
	}

	/**
	 * Attempts completion with all available Gemini models and API keys
	 * @param {object} env - Environment variables
	 * @param {object} config - AI configuration object
	 * @param {Array} contents - Array of message contents
	 * @param {ErrorAggregator} errorAggregator - Error aggregator instance
	 * @returns {Promise<object|null>} Response object or null if all attempts fail
	 */
	async attemptCompletion(env, config, contents, errorAggregator) {
		this.langSmithManager.ensureInitialized(env);
		const keys = this.apiKeyManager.loadGeminiApiKeys(env);

		for (let keyIndex = 0; keyIndex < keys.length; keyIndex++) {
			const apiKey = this.apiKeyManager.getNextGeminiApiKey(env);
			if (!apiKey) {
				throw new ApiKeyError('No Gemini API key available.');
			}

			const modelNames = (env.GEMINI_MODELS || DEFAULT_GEMINI_MODELS)
				.split(',')
				.map((m) => m.trim())
				.filter(Boolean);

			for (const modelName of modelNames) {
				try {
					return await this._attemptModelCompletion(modelName, config, apiKey, contents);
				} catch (error) {
					const context = `gemini/${modelName}`;
					console.error(`Failed chat completion with ${context}:`, error);
					errorAggregator.addError(context, error);
				}
			}
		}
		return null;
	}

	/**
	 * Attempts chat completion with a specific Gemini model
	 * @param {string} modelName - Name of the model to use
	 * @param {object} config - AI configuration object
	 * @param {string} apiKey - API key for authentication
	 * @param {Array} contents - Array of message contents
	 * @returns {Promise<object>} Response object with text and thoughts
	 * @private
	 */
	async _attemptModelCompletion(modelName, config, apiKey, contents) {
		console.log(`Attempting chat completion with model: ${modelName}`);

		const agent = ModelFactory.createGeminiAgent(apiKey, modelName, config);
		const messages = MessageConverter.convertToLangChainMessages(contents);

		const result = await agent.invoke({ messages });

		const lastMessage = result.messages[result.messages.length - 1];
		const responseContent = lastMessage.content;

		console.log(`Successfully completed chat with model: ${modelName}`);

		return {
			text: responseContent,
			thoughts: '', // LangChain doesn't expose thoughts in the same way
		};
	}
}
