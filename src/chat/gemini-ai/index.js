/**
 * @fileoverview Main entry point for the refactored GeminiAI module.
 *
 * This module provides a comprehensive interface for interacting with Google's Gemini AI models
 * and other AI services through LangChain. It includes:
 * - API key management with rotation and caching
 * - Model factory for creating different types of AI models
 * - Message conversion utilities for LangChain compatibility
 * - Embedding generation with error handling
 * - Comprehensive error handling with custom error classes
 * - LangSmith integration for tracing and monitoring
 *
 * <AUTHOR> for improved maintainability
 * @version 3.0.0
 */

// Export all constants
export * from './config/constants.js';

// Export all error classes
export * from './errors/GeminiErrors.js';

// Export all managers
export { ApiKeyManager } from './managers/ApiKeyManager.js';
export { LangSmithManager } from './managers/LangSmithManager.js';
export { EmbeddingManager } from './managers/EmbeddingManager.js';

// Export utilities
export { MessageConverter } from './utils/MessageConverter.js';

// Export factories
export { ModelFactory } from './factories/ModelFactory.js';

// Export core functionality
export { GeminiAI, callGenerativeAI, callFastGenerativeAI, generateEmbedding, getNextGeminiApiKey, getProviderPriorityOrder } from './core';
