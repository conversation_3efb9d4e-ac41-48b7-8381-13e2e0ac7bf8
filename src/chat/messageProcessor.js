import { SYSTEM_PROMPT, CHAT_TEMPERATURE, CHAT_THINKING_BUDGET } from '../constants.js';
import { sendTelegramError, sendTypingAction } from '../telegram.js';
import { extractMessageData, shouldProcessMessage } from './messageExtractor.js';
import { prepareChatContext, prepareFactContext, handleAIResponse } from './chatContext.js';
import { processAttachments } from './attachmentProcessor.js';
import { callGenerativeAI } from './geminiAI.js';
import { handleCommand } from './commandHandler.js';
import { formatTimestamp } from './utils/formatUtils.js';

// === Main Background Processing Function ===
export async function processTelegramMessageInBackground(env, webhookData) {
	let typingInterval;

	const startTypingIndicator = (chatId) => {
		sendTypingAction(env, chatId);
		typingInterval = setInterval(() => {
			sendTypingAction(env, chatId);
		}, 5000);
	};

	const stopTypingIndicator = () => {
		if (typingInterval) {
			clearInterval(typingInterval);
			typingInterval = null;
		}
	};

	const messageData = extractMessageData(env, webhookData);
	const { chatId, text, userId, username, firstName, messageId, messageDate } = messageData;
	const botUsername = env.TELEGRAM_BOT_USERNAME || '';

	if (!shouldProcessMessage(messageData, botUsername, webhookData)) {
		return;
	}

	startTypingIndicator(chatId);
	console.log(`Processing message from ${username} (${chatId}) in background: ${text}`);

	try {
		// Check if the message contains a command
		const commandHandled = await handleCommand(text, env, messageData, botUsername);
		if (commandHandled) {
			console.log(`Command processed successfully for chat ${chatId}`);
			stopTypingIndicator();
			return; // Exit early since command was handled
		}

		// Prepare chat context with previous messages and semantic history
		const { chatContent, previousMessages, redisHistory, semanticHistory } = await prepareChatContext(
			env,
			chatId,
			botUsername,
			text,
			firstName,
			messageDate,
			messageData
		);

		// Prepare user facts
		const { factsString } = await prepareFactContext(env, userId, firstName, text, previousMessages);

		// Process attachments if any
		const attachmentParts = await processAttachments(env, messageData);

		// Construct the enhanced system prompt with latest messages, memory, and user facts
		const enhancedSystemPrompt = SYSTEM_PROMPT.replace('{LATEST_MESSAGES}', redisHistory)
			.replace('{MEMORY}', semanticHistory)
			.replace('{USER_FACTS}', factsString)
			.replace('{CURRENT_DATETIME}', formatTimestamp(Date.now() / 1000, env)); // Convert to seconds for Unix timestamp;

		// Log the full system prompt for debugging
		console.log('SYSTEM_PROMPT:', enhancedSystemPrompt);

		// Adjust AI config based on response strategy
		const config = {
			temperature: CHAT_TEMPERATURE,
			responseMimeType: 'text/plain',
			systemInstruction: enhancedSystemPrompt,
			tools: [{ urlContext: {} }, { googleSearch: {} }],
			thinkingBudget: CHAT_THINKING_BUDGET,
		};

		const contents = [{ role: 'user', parts: [{ text: chatContent }, ...attachmentParts] }];
		const aiResponse = await callGenerativeAI(env, config, contents);

		stopTypingIndicator();
		await handleAIResponse(env, chatId, aiResponse.text, messageId, aiResponse.thoughts);
	} catch (backgroundError) {
		console.error('Error during background Telegram message processing:', backgroundError);
		const context = { path: '/hrmny (background)', method: 'POST', chatId: chatId };
		try {
			sendTelegramError(env, backgroundError, context);
		} catch (notificationError) {
			console.error('Failed to send background processing error notification:', notificationError);
		} finally {
			stopTypingIndicator();
		}
	}
}
