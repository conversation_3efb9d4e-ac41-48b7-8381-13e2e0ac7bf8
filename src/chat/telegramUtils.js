const TELEGRAM_API_URL = 'https://api.telegram.org';
const GET_FILE_ENDPOINT = '/bot{token}/getFile?file_id={fileId}';
const TELEGRAM_FILE_URL = '/file/bot{token}/{filePath}';

/**
 * Checks if the bot is mentioned in a message.
 */
export function checkBotMention(webhookData, botUsername) {
	if (!botUsername) {
		console.warn('TELEGRAM_BOT_USERNAME not configured, treating all messages as mentions');
		return true;
	}
	const message = webhookData.message || (webhookData.callback_query && webhookData.callback_query.message);
	if (message?.chat?.type === 'private') {
		return true;
	}
	const botMentionRegex = new RegExp(`@${botUsername}\\b`, 'i');
	if (botMentionRegex.test(message?.text)) {
		return true;
	}
	const entities = message?.entities || [];
	for (const entity of entities) {
		if (entity.type === 'mention') {
			const mentionText = message?.text.substring(entity.offset, entity.offset + entity.length);
			if (mentionText.toLowerCase() === `@${botUsername.toLowerCase()}`) {
				return true;
			}
		}
	}
	return message?.reply_to_message?.from?.username === botUsername;
}

/**
 * Escapes special characters for MarkdownV2 format in Telegram.
 */
export function escapeMdV2(text) {
	if (typeof text !== 'string') {
		return '';
	}
	// Convert **text** to *text*
	let processedText = text.replace(/\*\*(.*?)\*\*/g, '*$1*');
	// Escape standalone asterisks not part of ** pairs
	processedText = processedText.replace(/(?<!\*)\*(?!\*)/g, '');
	// Escape other special characters
	return processedText.replace(/[_[\]()~`>#+=|{}.!\\-]/g, '\\$&');
}

/**
 * Fetches JSON from a URL and throws an error if the response is not OK.
 */
export async function fetchJsonOrThrow(url, errorContext) {
	const response = await fetch(url);
	if (!response.ok) {
		throw new Error(`${errorContext} HTTP error! Status: ${response.status}`);
	}
	const json = await response.json();
	if (!json.ok) {
		throw new Error(`${errorContext} API error! Description: ${json.description}`);
	}
	return json;
}

/**
 * Gets the URL of a file from Telegram.
 */
export async function getTelegramFile(botToken, fileId) {
	try {
		const getFileUrl = `${TELEGRAM_API_URL}${GET_FILE_ENDPOINT.replace('{token}', botToken).replace('{fileId}', fileId)}`;
		const fileData = await fetchJsonOrThrow(getFileUrl, 'getFile:');
		const filePath = fileData.result.file_path;
		return `${TELEGRAM_API_URL}${TELEGRAM_FILE_URL.replace('{token}', botToken).replace('{filePath}', filePath)}`;
	} catch (err) {
		console.error('Error fetching Telegram file:', err.message);
		throw err;
	}
}

/**
 * Converts an ArrayBuffer to a Base64 string.
 */
export function arrayBufferToBase64(buffer) {
	let binary = '';
	const bytes = new Uint8Array(buffer);
	const len = bytes.byteLength;
	for (let i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return btoa(binary);
}
