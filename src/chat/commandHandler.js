import { deleteChatHistory } from '../redis.js';
import { deleteChatEmbeddings } from '../vectorStore.js';
import { sendLongTelegramMessage } from '../telegram.js';
import { escapeMdV2 } from './telegramUtils.js';

// Define supported commands
const COMMANDS = {
	CLEAR_HISTORY: ['/clear'],
};

/**
 * Checks if a message contains a supported command
 * @param {string} text - The message text to check
 * @returns {string|null} - The command type if found, null otherwise
 */
export function detectCommand(text) {
	if (!text || typeof text !== 'string') {
		return null;
	}

	const trimmedText = text.toLowerCase(); // Trim and convert to lowercase once

	for (const commandType in COMMANDS) {
		for (const command of COMMANDS[commandType]) {
			const lowerCaseCommand = command.toLowerCase();
			if (trimmedText === lowerCaseCommand || trimmedText.startsWith(lowerCaseCommand + ' ')) {
				return commandType; // Return the command type (e.g., 'CLEAR_HISTORY')
			}
		}
	}

	return null;
}

/**
 * Handles the clear history command
 * @param {object} env - Environment variables
 * @param {object} executionCtx - Execution context
 * @param {string} chatId - The chat ID
 * @param {string} userId - The user ID
 * @param {string} botUsername - The bot's username
 * @returns {Promise<boolean>} - True if command was handled successfully
 */
async function handleClearHistoryCommand(env, chatId, userId, botUsername) {
	try {
		console.log(`[handleClearHistoryCommand] Processing clear history command for chat ${chatId}, user ${userId}`);
		console.log(`[handleClearHistoryCommand] Bot username: "${botUsername}"`);
		console.log(`[handleClearHistoryCommand] Environment bot username: "${env.TELEGRAM_BOT_USERNAME}"`);

		// Track deletion results for operations
		const deletionResults = {
			redis: false,
			embeddings: false,
		};

		// Delete chat history from Redis
		try {
			console.log(`[handleClearHistoryCommand] Starting Redis chat history deletion...`);
			deletionResults.redis = await deleteChatHistory(env, chatId, botUsername);
			console.log(`[handleClearHistoryCommand] Redis chat history deletion result: ${deletionResults.redis}`);
		} catch (error) {
			console.error(`[handleClearHistoryCommand] Error deleting Redis chat history for chat ${chatId}:`, error);
			deletionResults.redis = false;
		}

		// Delete chat embeddings from vector store (this may take longer for large histories)
		try {
			console.log(`[handleClearHistoryCommand] Starting vector store cleanup for chat ${chatId}...`);

			deletionResults.embeddings = await deleteChatEmbeddings(env, chatId);
			console.log(`[handleClearHistoryCommand] Vector store cleanup completed for chat ${chatId}. Result: ${deletionResults.embeddings}`);
		} catch (error) {
			console.error(`[handleClearHistoryCommand] Error deleting chat embeddings for chat ${chatId}:`, error);
			deletionResults.embeddings = false;
		}

		// Prepare response message based on results
		let responseMessage;
		const allSuccessful = deletionResults.redis && deletionResults.embeddings;
		const anySuccessful = deletionResults.redis || deletionResults.embeddings;

		// Count successful operations for more detailed feedback
		const successCount = Object.values(deletionResults).filter((result) => result === true).length;
		const totalOperations = Object.keys(deletionResults).length;

		if (allSuccessful) {
			responseMessage = `✅ All data cleared successfully! We can start completely fresh! (${successCount}/${totalOperations} operations completed)`;
		} else if (anySuccessful) {
			responseMessage = `⚠️ Partial data cleared. ${successCount}/${totalOperations} operations completed successfully. Some items could not be deleted. Try again or contact support.`;
		} else {
			responseMessage = `❌ Failed to clear any data. Please try again later.`;
		}

		// Send response to user
		const escapedResponse = escapeMdV2(responseMessage);
		await sendLongTelegramMessage(env, chatId, escapedResponse);

		console.log(`Clear history command completed for chat ${chatId}. Results:`, deletionResults);
		return true;
	} catch (error) {
		console.error(`Error handling clear history command for chat ${chatId}:`, error);

		// Send error message to user
		const errorMessage = `❌ Error clearing chat history. Please try again later.`;

		try {
			const escapedErrorResponse = escapeMdV2(errorMessage);
			await sendLongTelegramMessage(env, chatId, escapedErrorResponse);
		} catch (sendError) {
			console.error(`Failed to send error message for clear history command:`, sendError);
		}

		return false;
	}
}

/**
 * Processes a detected command
 * @param {string} commandType - The type of command detected
 * @param {object} env - Environment variables
 * @param {object} executionCtx - Execution context
 * @param {object} messageData - The message data containing chat and user info
 * @param {string} botUsername - The bot's username
 * @returns {Promise<boolean>} - True if command was handled successfully
 */
export async function processCommand(commandType, env, messageData, botUsername) {
	const { chatId, userId } = messageData;

	switch (commandType) {
		case 'CLEAR_HISTORY':
			return await handleClearHistoryCommand(env, chatId, userId, botUsername);

		default:
			console.warn(`Unknown command type: ${commandType}`);
			return false;
	}
}

/**
 * Main function to handle command detection and processing
 * @param {string} text - The message text to check for commands
 * @param {object} env - Environment variables
 * @param {object} executionCtx - Execution context
 * @param {object} messageData - The message data containing chat and user info
 * @param {string} botUsername - The bot's username
 * @returns {Promise<boolean>} - True if a command was detected and handled
 */
export async function handleCommand(text, env, messageData, botUsername) {
	const commandType = detectCommand(text);

	if (!commandType) {
		return false; // No command detected
	}

	console.log(`Command detected: ${commandType} from user ${messageData.userId} in chat ${messageData.chatId}`);

	return await processCommand(commandType, env, messageData, botUsername);
}
