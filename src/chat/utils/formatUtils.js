/**
 * Format utilities for chat messages
 * @module formatUtils
 */

// Constants
const DATE_FORMAT_OPTIONS = {
	year: 'numeric',
	month: 'short',
	day: 'numeric',
	hour: '2-digit',
	minute: '2-digit',
};

/**
 * Formats a timestamp consistently across the application
 * @param {number} [timestamp] - Unix timestamp in seconds
 * @returns {string} Formatted date string
 */
export function formatTimestamp(timestamp, env) {
	return new Date((timestamp || 0) * 1000).toLocaleString('en-GB', {
		...DATE_FORMAT_OPTIONS,
		weekday: 'long',
		timeZone: env.TIMEZONE || 'UTC',
	});
}

/**
 * Determines the sender name based on message metadata
 * @param {Object} messageData - The message data
 * @param {Object} env - Environment variables
 * @returns {string} Sender's display name
 */
export function getSenderName(messageData, env) {
	if (messageData.role === 'assistant' || messageData.from?.is_bot) {
		return env.TELEGRAM_BOT_NAME || 'Bot';
	}
	return messageData.firstName || messageData.from?.first_name || messageData.from?.username || 'User';
}

/**
 * Formats a single message for display in chat history
 * @param {Object} messageData - The message data to format
 * @param {Object} env - Environment variables
 * @param {string} [customTimestamp] - Optional custom timestamp to use
 * @returns {string} Formatted message string
 */
export function formatMessage(messageData, env, customTimestamp = null) {
	const formattedTimestamp = customTimestamp || formatTimestamp(messageData.timestamp || messageData.date, env);
	const senderName = getSenderName(messageData, env);
	const textContent = messageData.text || '';

	return `<MESSAGE date="${formattedTimestamp}" from="${senderName}">${textContent}</MESSAGE>`;
}

/**
 * Formats the current user message
 * @param {string} messageText - The current message text
 * @param {string} senderName - Name of the message sender
 * @returns {string} Formatted current message
 */
export function formatCurrentMessage(messageText, senderName) {
	return `<USER_NAME>${senderName}</USER_NAME>\n<USER_MESSAGE>${messageText}</USER_MESSAGE>`;
}
