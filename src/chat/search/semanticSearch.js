/**
 * Semantic search and history management
 * @module semanticSearch
 */

import { generateEmbedding } from '../geminiAI.js';
import { getVectorStoreIndex } from '../../vectorStore.js';
import { formatMessage } from '../utils/formatUtils.js';

// Configuration
const SEMANTIC_SEARCH_CONFIG = {
	topK: 5,
	includeMetadata: true,
};

/**
 * Performs semantic search and returns formatted history with message IDs
 * @param {Object} env - Environment variables
 * @param {string|number} chatId - The chat ID to search within
 * @param {string} currentMessageText - The current message text to compare against
 * @returns {Promise<{semanticHistoryFormatted: string, semanticMessageTexts: Set<string>}>}
 */
export async function getHistory(env, chatId, currentMessageText) {
	let semanticHistoryFormatted = '';

	try {
		if (!currentMessageText) {
			return semanticHistoryFormatted;
		}

		const currentUserMessageEmbedding = await generateEmbedding(env, currentMessageText);
		if (!currentUserMessageEmbedding) {
			console.warn('Could not generate embedding for current message, skipping semantic search.');
			return semanticHistoryFormatted;
		}

		const vectorIndex = getVectorStoreIndex(env);
		const queryResult = await vectorIndex.query({
			vector: currentUserMessageEmbedding,
			topK: SEMANTIC_SEARCH_CONFIG.topK,
			includeMetadata: SEMANTIC_SEARCH_CONFIG.includeMetadata,
			filter: `chatId = ${chatId}`,
		});

		if (!queryResult?.length) {
			console.log('No relevant messages found in semantic search for this chat.');
			return semanticHistoryFormatted;
		}

		const filteredResults = _filterAndSortResults(queryResult, currentMessageText);

		semanticHistoryFormatted = filteredResults.map((match) => formatMessage(match.metadata, env)).join('\n');
		console.log('Semantic search results count (after filtering):', filteredResults.length);
	} catch (error) {
		console.error('Error during semantic search:', error);
	}

	return semanticHistoryFormatted;
}

/**
 * Filters out current message and sorts results by timestamp
 * @private
 * @param {Array} results - Search results to filter and sort
 * @param {string} currentMessageText - Current message text to exclude
 * @returns {Array} Filtered and sorted results
 */
function _filterAndSortResults(results, currentMessageText) {
	const normalizedCurrentText = currentMessageText.toLowerCase();

	return results
		.filter((match) => {
			if (!match.metadata?.text) return false;
			const normalizedMatchText = match.metadata.text.toLowerCase();
			return normalizedMatchText !== normalizedCurrentText;
		})
		.sort((a, b) => (a.metadata.timestamp || 0) - (b.metadata.timestamp || 0));
}
