import { Index } from '@upstash/vector/cloudflare';

export function getVectorStoreIndex(env) {
	const url = env.UPSTASH_VECTOR_REST_URL;
	const token = env.UPSTASH_VECTOR_REST_TOKEN;

	if (!url || !token) {
		throw new Error('Missing Upstash Vector environment variables: UPSTASH_VECTOR_REST_URL and/or UPSTASH_VECTOR_REST_TOKEN from env');
	}

	return new Index({
		url: url,
		token: token,
	});
}

/**
 * Checks if an error is related to API limits or quotas
 * @param {Error} error - The error to check
 * @returns {boolean} - True if the error is limit-related
 */
function isLimitError(error) {
	if (!error || !error.message) return false;

	const limitKeywords = ['rate limit', 'quota', 'max allowed read limit', 'too many requests', 'limit exceeded'];

	const errorMessage = error.message.toLowerCase();
	return limitKeywords.some((keyword) => errorMessage.includes(keyword));
}

/**
 * Deletes all vector embeddings for a specific chat ID using pagination to handle Upstash Vector limits
 * @param {object} env - Environment variables
 * @param {string} chatId - The chat ID to delete embeddings for
 * @returns {Promise<boolean>} - True if deletion was successful, false otherwise
 */
export async function deleteChatEmbeddings(env, chatId) {
	const BATCH_SIZE = 800; // Stay well under the 1000 read limit
	const MAX_BATCHES = 50; // Safety limit to prevent infinite loops
	const VECTOR_DIMENSION = 768; // Standard OpenAI embedding dimension

	try {
		const vectorIndex = getVectorStoreIndex(env);
		let totalDeleted = 0;
		let batchCount = 0;
		let hasMoreResults = true;

		console.log(`[deleteChatEmbeddings] Starting deletion of embeddings for chat ${chatId}`);
		console.log(`[deleteChatEmbeddings] Chat ID type: ${typeof chatId}, value: "${chatId}"`);

		// First, let's try to query without any filter to see what's in the vector store
		console.log(`[deleteChatEmbeddings] Testing vector store connectivity...`);

		// Create a more efficient dummy vector for metadata-only queries
		// Using a sparse vector with just a few non-zero values is more efficient
		const dummyVector = new Array(VECTOR_DIMENSION).fill(0);
		dummyVector[0] = 0.1; // Add minimal non-zero value for better query performance

		let foundEmbeddings = false;

		try {
			const testResult = await vectorIndex.query({
				vector: dummyVector,
				topK: 10,
				includeMetadata: true,
				filter: `chatId = ${chatId}`,
			});

			console.log(`[deleteChatEmbeddings] ${testResult.length} results`);

			console.log(`[deleteChatEmbeddings] Filter returned ${testResult.length || 0} results`);

			if (testResult.length > 0) {
				foundEmbeddings = true;
				console.log(`[deleteChatEmbeddings] Sample matching metadata:`, JSON.stringify(testResult[0].metadata, null, 2));
			}
		} catch (filterError) {
			console.error(`[deleteChatEmbeddings] Filter failed:`, filterError);
		}

		if (!foundEmbeddings) {
			console.log(`[deleteChatEmbeddings] No embeddings found for chat ${chatId} with any filter variation`);
			return true; // No embeddings to delete is considered successful
		}

		while (hasMoreResults && batchCount < MAX_BATCHES) {
			batchCount++;
			console.log(`[deleteChatEmbeddings] Processing batch ${batchCount} for chat ${chatId}...`);

			try {
				// Query for vectors with the specific chatId in smaller batches
				const queryResult = await vectorIndex.query({
					vector: dummyVector,
					topK: BATCH_SIZE,
					includeMetadata: true,
					filter: `chatId = ${chatId}`,
				});

				console.log(`[deleteChatEmbeddings] Batch ${batchCount} query returned ${queryResult.length || 0} results`);

				// Check if we found any results
				if (!queryResult || queryResult.length === 0) {
					console.log(`[deleteChatEmbeddings] No more embeddings found for chat ${chatId} in batch ${batchCount}`);
					hasMoreResults = false;
					break;
				}

				// Extract IDs of matching vectors in this batch
				const idsToDelete = queryResult.map((match) => match.id);
				console.log(
					`[deleteChatEmbeddings] Found ${idsToDelete.length} embeddings to delete in batch ${batchCount}: ${idsToDelete
						.slice(0, 5)
						.join(', ')}${idsToDelete.length > 5 ? '...' : ''}`
				);

				// Log some metadata samples for verification
				if (queryResult.length > 0) {
					const sampleMetadata = queryResult.slice(0, 3).map((m) => ({
						id: m.id,
						chatId: m.metadata?.chatId,
						role: m.metadata?.role,
						timestamp: m.metadata?.timestamp,
					}));
					console.log(`[deleteChatEmbeddings] Sample metadata from batch ${batchCount}:`, JSON.stringify(sampleMetadata, null, 2));
				}

				// Delete this batch of vectors
				if (idsToDelete.length > 0) {
					console.log(`[deleteChatEmbeddings] Attempting to delete ${idsToDelete.length} embeddings...`);
					const deleteResult = await vectorIndex.delete(idsToDelete);
					console.log(`[deleteChatEmbeddings] Delete operation result:`, deleteResult);

					totalDeleted += idsToDelete.length;
					console.log(
						`[deleteChatEmbeddings] Deleted ${idsToDelete.length} embeddings in batch ${batchCount}. Total deleted: ${totalDeleted}`
					);
				}

				// If we got fewer results than the batch size, we've reached the end
				if (queryResult.length < BATCH_SIZE) {
					hasMoreResults = false;
					console.log(
						`[deleteChatEmbeddings] Reached end of embeddings for chat ${chatId} (batch returned ${queryResult.length} < ${BATCH_SIZE})`
					);
				}

				// Add a small delay between batches to avoid overwhelming the API
				if (hasMoreResults) {
					await new Promise((resolve) => setTimeout(resolve, 100));
				}
			} catch (batchError) {
				console.error(`[deleteChatEmbeddings] Error processing batch ${batchCount} for chat ${chatId}:`, batchError);

				// If this is a rate limit or quota error, we should stop
				if (isLimitError(batchError)) {
					console.error(
						`[deleteChatEmbeddings] Hit API limits while deleting embeddings for chat ${chatId}. Stopping after ${totalDeleted} deletions.`
					);
					break;
				}

				// For other errors, continue with next batch but log the issue
				console.warn(`[deleteChatEmbeddings] Batch ${batchCount} failed, continuing with next batch...`);
			}
		}

		// Check if we hit the safety limit
		if (batchCount >= MAX_BATCHES) {
			console.warn(
				`[deleteChatEmbeddings] Reached maximum batch limit (${MAX_BATCHES}) for chat ${chatId}. Deleted ${totalDeleted} embeddings.`
			);
		}

		// Log final results
		if (totalDeleted > 0) {
			console.log(
				`[deleteChatEmbeddings] ✅ Successfully deleted ${totalDeleted} embeddings for chat ${chatId} across ${batchCount} batches`
			);
		} else {
			console.log(`[deleteChatEmbeddings] ℹ️  No embeddings found for chat ${chatId}`);
		}

		return true; // Return true even if we hit limits, as partial deletion is still progress
	} catch (error) {
		console.error(`[deleteChatEmbeddings] ❌ Error deleting chat embeddings for chat ${chatId}:`, error);
		console.error(`[deleteChatEmbeddings] Error stack:`, error.stack);
		return false;
	}
}
