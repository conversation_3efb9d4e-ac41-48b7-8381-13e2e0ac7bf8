import { USER_ERROR_MESSAGES } from './constants.js'; // Import the new error messages
import { escapeMdV2 } from './chat/telegramUtils.js'; // Import escapeMdV2

export const sendTelegramError = async (env, error, context = {}) => {
	const botToken = env.TELEGRAM_BOT_TOKEN;
	const chatId = env.TELEGRAM_CHAT_ID;

	if (!botToken || !chatId) {
		console.error('Telegram Bot Token or Chat ID is missing in environment variables.');
		return;
	}

	// Determine which user-friendly message to send
	let userFriendlyMessage = USER_ERROR_MESSAGES.GENERIC_ERROR;
	if (context.type === 'rate_limit') {
		userFriendlyMessage = USER_ERROR_MESSAGES.RATE_LIMIT_ERROR;
	} else if (context.type === 'attachment_processing') {
		userFriendlyMessage = USER_ERROR_MESSAGES.ATTACHMENT_PROCESSING_ERROR;
	}
	// Add more conditions here for other specific error types if needed

	// Log the detailed error internally
	const timestamp = new Date().toLocaleString('en-US', { timeZone: env.TIMEZONE });
	const internalErrorMessage = `🚨 Internal Error Alert 🚨

🕒 Time: ${timestamp} (${env.TIMEZONE})

📍 Path: ${context.path || 'N/A'}
🔗 Method: ${context.method || 'N/A'}
💬 Chat ID: ${context.chatId || 'N/A'}

💥 Message: ${error.message}

Stack:
<pre>${(error.stack || '').slice(0, 1000)}</pre>`; // Limit stack trace length for logs

	// console.error(internalErrorMessage);

	const telegramBotUrl = `https://api.telegram.org/bot${env.HRMNY_BOT_TOKEN}/sendMessage`;

	try {
		// Send the user-friendly message to the user's chat
		const response = await fetch(telegramBotUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				chat_id: context.chatId, // Send to the user's chat
				text: escapeMdV2(userFriendlyMessage), // Escape the message for MarkdownV2
				parse_mode: 'MarkdownV2',
			}),
		});

		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(
				`Failed to send user-friendly Telegram error message: ${response.status} ${response.statusText} - ${
					result.description || 'No description'
				}`
			);
		} else {
			console.log(`User-friendly error message sent to chat ${context.chatId}`);
		}

		const telegramUrl = `https://api.telegram.org/bot${botToken}/sendMessage`;

		const adminResponse = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				chat_id: chatId, // Send to the admin chat
				text: internalErrorMessage,
				parse_mode: 'HTML', // Use HTML for admin messages to preserve pre tag
			}),
		});

		if (!adminResponse.ok) {
			const adminResult = await adminResponse.json().catch(() => ({}));
			console.error(
				`Failed to send internal error report to admin chat: ${adminResponse.status} ${adminResponse.statusText} - ${
					adminResult.description || 'No description'
				}`
			);
		} else {
			console.log(`Internal error report sent to admin chat ${chatId}`);
		}
	} catch (err) {
		console.error('Error in sendTelegramError function itself:', err);
	}
};

/**
 * Sends a 'typing' chat action to a Telegram chat.
 * @param {object} env - Cloudflare environment variables (containing TELEGRAM_BOT_TOKEN).
 * @param {string|number} chatId - The ID of the chat to send the action to.
 * @returns {Promise<boolean>} - True if the action was sent successfully, false otherwise.
 */
export const sendTypingAction = async (env, chatId) => {
	const botToken = env.HRMNY_BOT_TOKEN; // Assuming HRMNY_BOT_TOKEN is the correct env var

	if (!botToken) {
		console.error('Telegram Bot Token is missing in environment variables for typing action.');
		return false;
	}

	if (!chatId) {
		console.error('Chat ID is required to send a typing action.');
		return false;
	}

	const telegramUrl = `https://api.telegram.org/bot${botToken}/sendChatAction`;

	const actionData = {
		chat_id: chatId,
		action: 'typing', // The action type
	};

	try {
		const response = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(actionData),
		});

		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(`Failed to send typing action: ${response.status} ${response.statusText} - ${result.description || 'No description'}`);
			return false;
		}

		// Telegram API returns { ok: true, result: true } on success for sendChatAction
		const responseData = await response.json();
		if (responseData.ok) {
			console.log(`Typing action sent to chat ID: ${chatId}`);
			return true;
		} else {
			console.error(`Telegram API reported failure for typing action: ${responseData.description}`);
			return false;
		}
	} catch (error) {
		console.error(`Error sending typing action to chat ID ${chatId}:`, error);
		return false;
	}
};

/**
 * Sends a message to a specific Telegram chat.
 * @param {object} env - Cloudflare environment variables (containing TELEGRAM_BOT_TOKEN).
 * @param {string|number} chatId - The ID of the chat to send the message to.
 * @param {string} text - The message text to send.
 * @param {object} options - Additional options for the message.
 * @param {string} [options.parseMode] - Parse mode for the message ('HTML', 'Markdown', or 'MarkdownV2').
 * @param {boolean} [options.replyToMessageId] - If the message is a reply, ID of the original message.
 * @returns {Promise<object|null>} - The response from the Telegram API or null if there was an error.
 */
export const sendTelegramMessage = async (env, chatId, text, options = {}) => {
	const botToken = env.HRMNY_BOT_TOKEN;

	if (!botToken) {
		console.error('Telegram Bot Token is missing in environment variables.');
		return null;
	}

	if (!chatId) {
		console.error('Chat ID is required to send a Telegram message.');
		return null;
	}

	const telegramUrl = `https://api.telegram.org/bot${botToken}/sendMessage`;

	const messageData = {
		chat_id: chatId,
		text: text,
		parse_mode: 'MarkdownV2',
		reply_parameters: options,
	};

	console.log(JSON.stringify(messageData));

	try {
		const response = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(messageData),
		});

		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(
				`Failed to send Telegram message: ${response.status} ${response.statusText} - ${result.description || 'No description'}`
			);
			return null;
		}

		const responseData = await response.json();
		console.log(`Telegram message sent to chat ID: ${chatId}`);
		return responseData;
	} catch (error) {
		console.error(`Error sending Telegram message to chat ID ${chatId}:`, error);
		return null;
	}
};

export const MAX_MESSAGE_LENGTH = 2048; // Telegram's message character limit

/**
 * Sends a potentially long message to a Telegram chat, splitting it into multiple messages if necessary.
 * @param {object} env - Cloudflare environment variables (containing TELEGRAM_BOT_TOKEN and HRMNY_BOT_TOKEN).
 * @param {string|number} chatId - The ID of the chat to send the message to.
 * @param {string} text - The message text to send.
 * @param {object} options - Additional options for the message (e.g., message_id).
 * @returns {Promise<boolean>} - Returns true if all messages were sent successfully.
 */
export const sendLongTelegramMessage = async (env, chatId, text, options = {}) => {
	if (text.length <= MAX_MESSAGE_LENGTH) {
		await sendTelegramMessage(env, chatId, text, options);
		return true;
	}

	const chunks = [];
	let currentChunk = '';
	const lines = text.split('\n');

	for (const line of lines) {
		// If adding the next line exceeds the limit, push the current chunk and start a new one
		if ((currentChunk + line).length + 1 > MAX_MESSAGE_LENGTH && currentChunk.length > 0) {
			chunks.push(currentChunk);
			currentChunk = '';
		}
		// Add the line to the current chunk
		currentChunk += (currentChunk.length > 0 ? '\n' : '') + line;

		// If a single line is longer than MAX_MESSAGE_LENGTH, split it
		while (currentChunk.length > MAX_MESSAGE_LENGTH) {
			let splitPoint = MAX_MESSAGE_LENGTH;
			// Try to split at a natural break (e.g., last space before the limit)
			const tempChunk = currentChunk.substring(0, MAX_MESSAGE_LENGTH);
			const lastSpaceIndex = tempChunk.lastIndexOf(' ');
			if (lastSpaceIndex > MAX_MESSAGE_LENGTH * 0.8) {
				// Only use if it's not too close to the beginning
				splitPoint = lastSpaceIndex;
			}

			chunks.push(currentChunk.substring(0, splitPoint));
			currentChunk = currentChunk.substring(splitPoint).trimStart(); // Remove leading whitespace from the next chunk
		}
	}

	// Add the last remaining chunk
	if (currentChunk.length > 0) {
		chunks.push(currentChunk);
	}

	let firstMessage = true;
	for (const chunk of chunks) {
		const currentOptions = { ...options };
		if (!firstMessage) {
			// For subsequent messages, remove reply_parameters to avoid replying to the same message multiple times
			delete currentOptions.message_id;
		}
		await sendTelegramMessage(env, chatId, chunk, currentOptions);
		firstMessage = false;
		await new Promise((resolve) => setTimeout(resolve, 500)); // Add a small delay between messages
	}

	return true;
};
