# Amai Lyrics API

A high-performance lyrics API built on Cloudflare Workers that fetches synchronized lyrics for Spotify tracks. The API integrates with multiple lyrics providers and includes intelligent caching, rate limiting, and Telegram bot integration.

## Features

- **Multi-source lyrics fetching**: Integrates with Beautiful-Lyrics, LRCLib, and Musixmatch APIs
- **Intelligent caching**: Redis-based caching for improved performance
- **Rate limiting**: Built-in protection against abuse
- **Telegram bot integration**: Automated notifications and chat processing
- **CORS support**: Ready for web application integration
- **User tracking**: Analytics and usage monitoring
- **Health monitoring**: Built-in health check endpoints

## API Endpoints

### Core Endpoints

#### `POST /lyrics/:id`

Fetch lyrics for a Spotify track by track ID.

**Parameters:**

- `id` (path): Spotify track ID

**Headers:**

- `Authorization` (optional): Spotify access token for enhanced track data

**Response:**

```json
{
	"track": {
		"name": "Song Title",
		"artist": "Artist Name",
		"album": "Album Name",
		"duration": 180000
	},
	"lyrics": {
		"syncType": "LINE_SYNCED",
		"lines": [
			{
				"startTimeMs": "0",
				"words": "First line of lyrics"
			}
		]
	}
}
```

**Error Responses:**

- `400`: Track ID missing
- `404`: Lyrics not found
- `500`: Internal server error

#### `GET /health`

Health check endpoint for monitoring.

**Response:**

```json
{
	"status": "healthy",
	"timestamp": "2024-01-01T00:00:00.000Z",
	"environment": "production"
}
```

#### `GET /ping`

Simple ping endpoint.

**Response:** `pong`

### Telegram Integration

#### `POST /hrmny`

Advanced webhook endpoint for Telegram bot integration featuring AI-powered conversational responses.

**Features:**

- **AI-Powered Conversations**: Uses Google Gemini AI with custom personality (Harmony/Mon)
- **Multi-language Support**: Primarily Indonesian with organic Japanese phrases
- **Context-Aware Responses**: Maintains conversation history and user facts
- **Media Processing**: Supports photos, documents, and media groups
- **Smart Message Filtering**: Processes mentions, private messages, and media content
- **Typing Indicators**: Shows realistic typing status during processing
- **Reply Context**: Understands and responds to replied messages
- **User Fact Learning**: Remembers and updates user information over time

**Supported Content Types:**

- Text messages (with bot mentions in groups)
- Photos (JPEG, PNG)
- Documents (PDF, TXT)
- Media groups (multiple files)
- Reply-to messages with context

**Bot Behavior:**

- **Personality**: Friendly, casual, and conversational AI named Harmony
- **Language**: Casual Indonesian with occasional Japanese phrases
- **Response Style**: Concise, natural, and context-appropriate
- **Memory**: Maintains user facts and conversation history
- **Error Handling**: Graceful error recovery with notifications

**Processing Logic:**

1. **Message Validation**: Checks for valid content and bot mentions
2. **Context Preparation**: Gathers chat history and user facts
3. **Media Processing**: Downloads and analyzes attachments
4. **AI Generation**: Creates contextual responses using Gemini AI
5. **Response Delivery**: Sends formatted replies with typing indicators

**Request Format:**
Standard Telegram webhook payload with support for:

- Text messages
- Photo messages with captions
- Document messages
- Media group messages
- Callback queries

**Response:**

```json
{
	"success": true,
	"message": "Webhook received"
}
```

**Error Handling:**

- Automatic error notifications to administrators
- Graceful fallbacks for processing failures
- Comprehensive logging for debugging

**Planned Features:**

- **Advanced Memory**: Long-term memory with semantic search capabilities
- **Real-time Notifications**: Push notifications for important events

## Deployment

This project is deployed on Cloudflare Workers using Wrangler.

### Requirements

- Node.js 18+
- npm or yarn
- Cloudflare account with Workers enabled
- Upstash Redis instance
- Telegram bot token (for bot features)

### Environment Variables

Configure these in your `wrangler.toml`:

```toml
[vars]
DEV_MODE = "false"
PROTOCOL = "https"
HOSTNAME = "your-worker.workers.dev"
TELEGRAM_BOT_USERNAME = "your_bot"
```

### Setup

1. **Clone the repository:**

   ```sh
   git clone <repository-url>
   cd amai-cw
   ```

2. **Install dependencies:**

   ```sh
   npm install
   ```

3. **Configure Wrangler:**

   - Update `wrangler.toml` with your Cloudflare account details
   - Set up environment variables and secrets

4. **Deploy:**
   ```sh
   npm run deploy
   ```

### Development

To start the development server:

```sh
npm run dev
```

For public development (accessible from external networks):

```sh
npm run dev:public
```

## Architecture

- **Framework**: Hono.js for lightweight, fast routing
- **Runtime**: Cloudflare Workers
- **Caching**: Upstash Redis
- **Rate Limiting**: Cloudflare Workers rate limiting
- **AI Integration**: Google Gemini AI for Telegram bot responses

## License

This project is private and proprietary.
